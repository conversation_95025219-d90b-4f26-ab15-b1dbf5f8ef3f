package com.example.service;

import com.example.model.Device;
import com.example.model.DataItem;
import com.example.model.DataHistory;
import com.example.repository.DeviceRepository;
import com.example.repository.DataItemRepository;
import com.example.repository.DataHistoryRepository;
import com.example.service.connector.DataSourceConnector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BiDataService {
    
    @Autowired
    private DeviceRepository deviceRepository;
    
    @Autowired
    private DataItemRepository dataItemRepository;

    @Autowired
    private DataSetService dataSetService;
    
    @Autowired
    private DataHistoryRepository dataHistoryRepository;
    
    /**
     * 获取所有设备列表（用于数据源选择）
     */
    public List<Map<String, Object>> getAllDevicesForSelect() {
        List<Device> devices = deviceRepository.findAll();
        return devices.stream().map(device -> {
            Map<String, Object> deviceMap = new HashMap<>();
            deviceMap.put("id", device.getId());
            deviceMap.put("name", device.getName());
            deviceMap.put("address", device.getAddress());
            deviceMap.put("port", device.getPort());
            deviceMap.put("connected", device.getConnected());
            return deviceMap;
        }).collect(Collectors.toList());
    }
    
    /**
     * 获取指定设备的数据项列表（用于监控项选择）
     */
    public List<Map<String, Object>> getDeviceDataItems(String deviceId) {
        List<DataItem> dataItems = dataItemRepository.findByDeviceId(deviceId);
        return dataItems.stream().map(item -> {
            Map<String, Object> itemMap = new HashMap<>();
            itemMap.put("id", item.getId());
            itemMap.put("name", item.getName());
            itemMap.put("address", item.getAddress());
            itemMap.put("refreshInterval", item.getRefreshInterval());
            itemMap.put("latestValue", item.getLatestValue());
            itemMap.put("historyEnabled", item.getHistoryEnabled());
            itemMap.put("deviceName", item.getDevice().getName());
            return itemMap;
        }).collect(Collectors.toList());
    }

    /**
     * 获取单个监控项的实时数据
     */
    public Map<String, Object> getDataItemRealTimeValue(String dataItemId) {
        Map<String, Object> result = new HashMap<>();

        try {
            Optional<DataItem> dataItemOpt = dataItemRepository.findById(dataItemId);
            if (dataItemOpt.isPresent()) {
                DataItem dataItem = dataItemOpt.get();

                // 如果latestValue为null，生成模拟数据用于测试
                Integer value = dataItem.getLatestValue();
                if (value == null) {
                    // 生成20-100之间的随机数作为模拟数据
                    value = 20 + (int)(Math.random() * 80);
                    log.info("为监控项 {} 生成模拟数据: {}", dataItem.getName(), value);
                }

                result.put("success", true);
                result.put("value", value);
                result.put("name", dataItem.getName());
                result.put("address", dataItem.getAddress());
                result.put("deviceName", dataItem.getDevice().getName());
                result.put("timestamp", LocalDateTime.now());
            } else {
                result.put("success", false);
                result.put("error", "监控项不存在");
            }
        } catch (Exception e) {
            log.error("获取监控项实时数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取监控项的历史数据（用于图表显示）
     */
    public Map<String, Object> getDataItemHistoryData(String dataItemId, int hours) {
        Map<String, Object> result = new HashMap<>();

        try {
            Optional<DataItem> dataItemOpt = dataItemRepository.findById(dataItemId);
            if (!dataItemOpt.isPresent()) {
                result.put("success", false);
                result.put("error", "监控项不存在");
                return result;
            }

            DataItem dataItem = dataItemOpt.get();

            // 检查是否启用了历史记录
            if (!dataItem.getHistoryEnabled()) {
                result.put("success", false);
                result.put("error", "该监控项未启用历史记录");
                return result;
            }

            // 获取指定时间范围的历史数据
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusHours(hours);

            List<DataHistory> historyData = dataHistoryRepository
                .findByDataItemAndTimestampBetween(dataItem, startTime, endTime);

            List<String> labels = new ArrayList<>();
            List<Integer> values = new ArrayList<>();

            // 如果数据太多，进行采样
            int maxPoints = 100; // 最多显示100个数据点
            int step = Math.max(1, historyData.size() / maxPoints);

            for (int i = 0; i < historyData.size(); i += step) {
                DataHistory data = historyData.get(i);
                labels.add(data.getTimestamp().format(java.time.format.DateTimeFormatter.ofPattern("MM-dd HH:mm")));
                values.add(data.getValue());
            }

            result.put("success", true);
            result.put("labels", labels);
            result.put("values", values);
            result.put("dataItemName", dataItem.getName());
            result.put("deviceName", dataItem.getDevice().getName());
            result.put("totalPoints", historyData.size());

        } catch (Exception e) {
            log.error("获取监控项历史数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取监控项的最新N条历史数据
     */
    public Map<String, Object> getDataItemLatestHistoryData(String dataItemId, int count) {
        return getDataItemLatestHistoryData(dataItemId, count, "datetime");
    }

    /**
     * 获取监控项的最新N条历史数据（支持时间格式）
     */
    public Map<String, Object> getDataItemLatestHistoryData(String dataItemId, int count, String timeFormat) {
        Map<String, Object> result = new HashMap<>();

        try {
            Optional<DataItem> dataItemOpt = dataItemRepository.findById(dataItemId);
            if (!dataItemOpt.isPresent()) {
                result.put("success", false);
                result.put("error", "监控项不存在");
                return result;
            }

            DataItem dataItem = dataItemOpt.get();

            // 检查是否启用了历史记录
            if (!dataItem.getHistoryEnabled()) {
                result.put("success", false);
                result.put("error", "该监控项未启用历史记录");
                return result;
            }

            // 获取最新的N条历史数据
            List<DataHistory> historyData = dataHistoryRepository
                .findByDataItemOrderByTimestampDesc(dataItem);

            List<String> labels = new ArrayList<>();
            List<Integer> values = new ArrayList<>();

            // 根据timeFormat选择时间格式
            String pattern;
            switch (timeFormat) {
                case "date":
                    pattern = "MM-dd";
                    break;
                case "time":
                    pattern = "HH:mm";
                    break;
                case "datetime":
                default:
                    pattern = "MM-dd HH:mm";
                    break;
            }

            // 取最新的count条数据，然后反转顺序（从旧到新）
            int actualCount = Math.min(count, historyData.size());
            for (int i = actualCount - 1; i >= 0; i--) {
                DataHistory data = historyData.get(i);
                labels.add(data.getTimestamp().format(java.time.format.DateTimeFormatter.ofPattern(pattern)));
                values.add(data.getValue());
            }

            result.put("success", true);
            result.put("labels", labels);
            result.put("values", values);
            result.put("dataItemName", dataItem.getName());
            result.put("deviceName", dataItem.getDevice().getName());
            result.put("totalPoints", actualCount);
            result.put("requestedCount", count);

        } catch (Exception e) {
            log.error("获取监控项最新历史数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }
    
    /**
     * 获取组件数据（根据组件类型和配置）
     */
    public Map<String, Object> getWidgetData(String widgetType, Map<String, Object> dataSourceConfig) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("获取组件数据 - widgetType: {}, dataSourceConfig: {}", widgetType, dataSourceConfig);

            String dataItemId = (String) dataSourceConfig.get("dataItemId");
            String dataMode = (String) dataSourceConfig.get("dataMode"); // "realtime" 或 "history"
            String timeFormat = (String) dataSourceConfig.getOrDefault("timeFormat", "datetime");

            log.info("解析参数 - dataItemId: {}, dataMode: {}, timeFormat: {}", dataItemId, dataMode, timeFormat);

            if ("line-chart".equals(widgetType) || "bar-chart".equals(widgetType) || "horizontal-bar-chart".equals(widgetType)) {
                // 图表组件需要历史数据
                if (dataItemId != null && !dataItemId.isEmpty()) {
                    if ("history".equals(dataMode)) {
                        // 获取最新N条历史数据
                        Integer count = (Integer) dataSourceConfig.getOrDefault("historyCount", 50);
                        result = getDataItemLatestHistoryData(dataItemId, count, timeFormat);
                    } else {
                        // 获取最近的一些数据点作为实时趋势
                        result = getDataItemRecentTrend(dataItemId, 20, timeFormat);
                    }
                } else {
                    // 没有配置数据源，返回示例数据
                    result = getExampleChartData(widgetType);
                }

            } else if ("gauge-chart".equals(widgetType)) {
                // 仪表盘组件需要实时单值数据
                if (dataItemId != null && !dataItemId.isEmpty()) {
                    result = getDataItemRealTimeValue(dataItemId);
                } else {
                    // 没有配置数据源，返回示例数据
                    result.put("success", true);
                    result.put("value", 75);
                    result.put("unit", "%");
                    result.put("name", "示例数据");
                }

            } else if ("water-chart".equals(widgetType)) {
                // 水波图组件需要实时单值数据
                if (dataItemId != null && !dataItemId.isEmpty()) {
                    result = getDataItemRealTimeValue(dataItemId);

                    // 处理目标值配置
                    Boolean enableWaterTarget = (Boolean) dataSourceConfig.get("enableWaterTarget");
                    if (enableWaterTarget != null && enableWaterTarget) {
                        String targetSource = (String) dataSourceConfig.get("waterTargetSource");
                        if ("dataItem".equals(targetSource)) {
                            // 目标值来源是监控项数据
                            String targetDataItemId = (String) dataSourceConfig.get("waterTargetDataItem");
                            if (targetDataItemId != null && !targetDataItemId.isEmpty()) {
                                Map<String, Object> targetData = getDataItemRealTimeValue(targetDataItemId);
                                if ((Boolean) targetData.get("success")) {
                                    result.put("targetValue", targetData.get("value"));
                                }
                            }
                        } else {
                            // 目标值来源是手动输入
                            Integer targetValue = (Integer) dataSourceConfig.get("waterTargetValue");
                            if (targetValue != null) {
                                result.put("targetValue", targetValue);
                            }
                        }
                    }
                } else {
                    // 没有配置数据源，返回示例数据
                    result.put("success", true);
                    result.put("value", 65);
                    result.put("targetValue", 100);
                    result.put("name", "示例水波图");
                }

            } else if ("column-percentage-chart".equals(widgetType)) {
                // 柱状百分比图组件需要实时单值数据
                if (dataItemId != null && !dataItemId.isEmpty()) {
                    result = getDataItemRealTimeValue(dataItemId);

                    // 处理目标值配置
                    Boolean enableTarget = (Boolean) dataSourceConfig.get("enableColumnTarget");
                    System.out.println("柱状百分比图目标值配置 - enableTarget: " + enableTarget);

                    if (enableTarget != null && enableTarget) {
                        String targetSource = (String) dataSourceConfig.get("columnTargetSource");
                        System.out.println("柱状百分比图目标值来源: " + targetSource);

                        if ("dataItem".equals(targetSource)) {
                            // 目标值来源是监控项数据
                            String targetDataItemId = (String) dataSourceConfig.get("columnTargetDataItem");
                            System.out.println("柱状百分比图目标值监控项ID: " + targetDataItemId);

                            if (targetDataItemId != null && !targetDataItemId.isEmpty()) {
                                Map<String, Object> targetData = getDataItemRealTimeValue(targetDataItemId);
                                if ((Boolean) targetData.get("success")) {
                                    Object targetValue = targetData.get("value");
                                    result.put("targetValue", targetValue);
                                    System.out.println("柱状百分比图从监控项获取目标值: " + targetValue);
                                }
                            }
                        } else {
                            // 目标值来源是手动输入
                            Object targetValue = dataSourceConfig.get("columnTargetValue");
                            System.out.println("柱状百分比图手动输入目标值: " + targetValue);

                            if (targetValue != null) {
                                result.put("targetValue", targetValue);
                                System.out.println("柱状百分比图设置目标值: " + targetValue);
                            }
                        }
                    }
                } else {
                    // 没有配置数据源，返回示例数据
                    result.put("success", true);
                    result.put("value", 75);
                    result.put("targetValue", 100);
                    result.put("name", "示例柱状百分比图");
                }

            } else if ("text-label".equals(widgetType)) {
                // 文本组件需要实时单值数据
                if (dataItemId != null && !dataItemId.isEmpty()) {
                    result = getDataItemRealTimeValue(dataItemId);
                } else {
                    // 没有配置数据源，返回示例数据
                    result.put("success", true);
                    result.put("value", "示例文本");
                    result.put("name", "示例数据");
                }

            } else if ("html-widget".equals(widgetType)) {
                // HTML组件不需要数据源，返回成功状态
                result.put("success", true);
                result.put("message", "HTML组件无需数据源");
                result.put("htmlContent", "");  // 可以从配置中获取HTML内容

            } else if ("data-table".equals(widgetType)) {
                // 数据表格支持多种数据源类型
                String dataSourceType = (String) dataSourceConfig.get("dataSourceType");

                if ("multiData".equals(dataSourceType)) {
                    // 多数据源：显示多个监控项的数据
                    result = getMultiDataItemsTable(dataSourceConfig);
                } else if ("static".equals(dataSourceType)) {
                    // 静态数据源：显示静态表格数据
                    result = getStaticDataTable(dataSourceConfig);
                } else if ("externalData".equals(dataSourceType)) {
                    // 外部数据源：显示外部数据集的数据
                    result = getExternalDataTable(dataSourceConfig);
                } else {
                    // 默认监控项数据源：显示设备和监控项信息
                    String deviceId = (String) dataSourceConfig.get("deviceId");
                    if (deviceId != null && !deviceId.isEmpty()) {
                        result = getDeviceDataItemsTable(deviceId);
                    } else {
                        result = getAllDevicesTable();
                    }
                }
            }

            if (!result.containsKey("success")) {
                result.put("success", true);
                result.put("timestamp", LocalDateTime.now());
            }

        } catch (Exception e) {
            log.error("获取组件数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取监控项的最近趋势数据
     */
    private Map<String, Object> getDataItemRecentTrend(String dataItemId, int pointCount) {
        return getDataItemRecentTrend(dataItemId, pointCount, "datetime");
    }

    /**
     * 获取监控项的最近趋势数据（支持时间格式）
     */
    private Map<String, Object> getDataItemRecentTrend(String dataItemId, int pointCount, String timeFormat) {
        Map<String, Object> result = new HashMap<>();

        try {
            Optional<DataItem> dataItemOpt = dataItemRepository.findById(dataItemId);
            if (!dataItemOpt.isPresent()) {
                result.put("success", false);
                result.put("error", "监控项不存在");
                return result;
            }

            DataItem dataItem = dataItemOpt.get();

            if (!dataItem.getHistoryEnabled()) {
                // 如果没有历史记录，返回当前值的平线
                List<String> labels = new ArrayList<>();
                List<Integer> values = new ArrayList<>();
                Integer currentValue = dataItem.getLatestValue() != null ? dataItem.getLatestValue() : 0;

                for (int i = 0; i < pointCount; i++) {
                    labels.add(String.valueOf(i));
                    values.add(currentValue);
                }

                result.put("success", true);
                result.put("labels", labels);
                result.put("values", values);
                result.put("dataItemName", dataItem.getName());
                return result;
            }

            // 获取最近的历史数据
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusHours(1); // 最近1小时

            List<DataHistory> historyData = dataHistoryRepository
                .findByDataItemAndTimestampBetween(dataItem, startTime, endTime);

            List<String> labels = new ArrayList<>();
            List<Integer> values = new ArrayList<>();

            // 根据timeFormat选择时间格式
            String pattern;
            switch (timeFormat) {
                case "date":
                    pattern = "MM-dd";
                    break;
                case "time":
                    pattern = "HH:mm";
                    break;
                case "datetime":
                default:
                    pattern = "MM-dd HH:mm";
                    break;
            }

            if (historyData.isEmpty()) {
                // 没有历史数据，返回当前值
                Integer currentValue = dataItem.getLatestValue() != null ? dataItem.getLatestValue() : 0;
                for (int i = 0; i < pointCount; i++) {
                    labels.add(String.valueOf(i));
                    values.add(currentValue);
                }
            } else {
                // 取最近的数据点
                int step = Math.max(1, historyData.size() / pointCount);
                for (int i = 0; i < historyData.size(); i += step) {
                    DataHistory data = historyData.get(i);
                    labels.add(data.getTimestamp().format(java.time.format.DateTimeFormatter.ofPattern(pattern)));
                    values.add(data.getValue());
                }
            }

            result.put("success", true);
            result.put("labels", labels);
            result.put("values", values);
            result.put("dataItemName", dataItem.getName());

        } catch (Exception e) {
            log.error("获取监控项趋势数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }
    
    /**
     * 获取历史数据（用于图表显示）
     */
    public Map<String, Object> getHistoryData(String deviceId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取指定设备的数据项
            List<DataItem> dataItems = dataItemRepository.findByDeviceId(deviceId);
            if (!dataItems.isEmpty()) {
                DataItem firstItem = dataItems.get(0);

                List<DataHistory> historyData = dataHistoryRepository
                    .findByDataItemAndTimestampBetween(firstItem, startTime, endTime);

                List<String> labels = new ArrayList<>();
                List<Integer> values = new ArrayList<>();

                for (DataHistory data : historyData) {
                    labels.add(data.getTimestamp().toString());
                    values.add(data.getValue());
                }

                result.put("labels", labels);
                result.put("values", values);
            } else {
                result.put("labels", new ArrayList<>());
                result.put("values", new ArrayList<>());
            }
            result.put("success", true);

        } catch (Exception e) {
            log.error("获取历史数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }
    
    /**
     * 获取设备统计信息
     */
    public Map<String, Object> getDeviceStatistics() {
        Map<String, Object> result = new HashMap<>();

        try {
            List<Device> devices = deviceRepository.findAll();
            long totalDevices = devices.size();
            long connectedDevices = devices.stream().mapToLong(d -> d.getConnected() ? 1 : 0).sum();
            long disconnectedDevices = totalDevices - connectedDevices;

            result.put("total", totalDevices);
            result.put("connected", connectedDevices);
            result.put("disconnected", disconnectedDevices);
            result.put("connectionRate", totalDevices > 0 ? (double) connectedDevices / totalDevices * 100 : 0);
            result.put("success", true);

        } catch (Exception e) {
            log.error("获取设备统计失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取示例图表数据
     */
    private Map<String, Object> getExampleChartData(String widgetType) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);

        if ("line-chart".equals(widgetType)) {
            result.put("labels", Arrays.asList("00:00", "04:00", "08:00", "12:00", "16:00", "20:00"));
            result.put("values", Arrays.asList(10, 25, 35, 45, 30, 20));
            result.put("dataItemName", "示例折线图");
        } else if ("bar-chart".equals(widgetType)) {
            result.put("labels", Arrays.asList("设备A", "设备B", "设备C", "设备D"));
            result.put("values", Arrays.asList(65, 59, 80, 81));
            result.put("dataItemName", "示例柱状图");
        } else if ("horizontal-bar-chart".equals(widgetType)) {
            result.put("labels", Arrays.asList("产线A", "产线B", "产线C", "产线D"));
            result.put("values", Arrays.asList(75, 68, 92, 87));
            result.put("dataItemName", "示例水平柱状图");
        }

        return result;
    }

    /**
     * 获取设备的监控项表格数据
     */
    private Map<String, Object> getDeviceDataItemsTable(String deviceId) {
        Map<String, Object> result = new HashMap<>();

        try {
            List<DataItem> dataItems = dataItemRepository.findByDeviceId(deviceId);
            List<Map<String, Object>> tableData = dataItems.stream().map(item -> {
                Map<String, Object> row = new HashMap<>();
                row.put("name", item.getName());
                row.put("address", item.getAddress());
                row.put("value", item.getLatestValue() != null ? item.getLatestValue() : 0);
                row.put("status", item.getDevice().getConnected() ? "在线" : "离线");
                return row;
            }).collect(Collectors.toList());

            result.put("success", true);
            result.put("data", tableData);

        } catch (Exception e) {
            log.error("获取设备监控项表格数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取所有设备表格数据
     */
    private Map<String, Object> getAllDevicesTable() {
        Map<String, Object> result = new HashMap<>();

        try {
            List<Device> devices = deviceRepository.findAll();
            List<Map<String, Object>> tableData = devices.stream().map(device -> {
                Map<String, Object> row = new HashMap<>();
                row.put("name", device.getName());
                row.put("address", device.getAddress() + ":" + device.getPort());
                row.put("status", device.getConnected() ? "已连接" : "未连接");

                // 获取该设备的监控项数量
                List<DataItem> items = dataItemRepository.findByDeviceId(device.getId());
                row.put("itemCount", items.size());

                return row;
            }).collect(Collectors.toList());

            result.put("success", true);
            result.put("data", tableData);

        } catch (Exception e) {
            log.error("获取设备表格数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取多数据源表格数据
     */
    private Map<String, Object> getMultiDataItemsTable(Map<String, Object> dataSourceConfig) {
        Map<String, Object> result = new HashMap<>();

        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> multiDataSources = (List<Map<String, Object>>) dataSourceConfig.get("multiDataSources");

            if (multiDataSources == null || multiDataSources.isEmpty()) {
                result.put("success", false);
                result.put("error", "多数据源配置为空");
                return result;
            }

            List<Map<String, Object>> tableData = new ArrayList<>();

            for (Map<String, Object> source : multiDataSources) {
                String dataItemId = (String) source.get("dataItemId");
                String label = (String) source.get("label");

                if (dataItemId != null && !dataItemId.isEmpty()) {
                    Optional<DataItem> dataItemOpt = dataItemRepository.findById(dataItemId);
                    if (dataItemOpt.isPresent()) {
                        DataItem dataItem = dataItemOpt.get();
                        Map<String, Object> row = new HashMap<>();
                        row.put("name", label != null ? label : dataItem.getName());
                        row.put("value", dataItem.getLatestValue() != null ? dataItem.getLatestValue() : 0);
                        tableData.add(row);
                    }
                }
            }

            result.put("success", true);
            result.put("data", tableData);

        } catch (Exception e) {
            log.error("获取多数据源表格数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取静态数据表格
     */
    private Map<String, Object> getStaticDataTable(Map<String, Object> dataSourceConfig) {
        Map<String, Object> result = new HashMap<>();

        try {
            String staticLabels = (String) dataSourceConfig.get("staticLabels");
            String staticValues = (String) dataSourceConfig.get("staticValues");

            if (staticLabels == null || staticValues == null) {
                result.put("success", false);
                result.put("error", "静态数据配置不完整");
                return result;
            }

            String[] labels = staticLabels.split("\n");
            String[] values = staticValues.split("\n");

            List<Map<String, Object>> tableData = new ArrayList<>();
            int maxLength = Math.min(labels.length, values.length);

            for (int i = 0; i < maxLength; i++) {
                if (!labels[i].trim().isEmpty() && !values[i].trim().isEmpty()) {
                    Map<String, Object> row = new HashMap<>();
                    row.put("name", labels[i].trim());
                    row.put("value", values[i].trim());
                    tableData.add(row);
                }
            }

            result.put("success", true);
            result.put("data", tableData);

        } catch (Exception e) {
            log.error("获取静态数据表格失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取外部数据源表格数据
     */
    private Map<String, Object> getExternalDataTable(Map<String, Object> dataSourceConfig) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查是否为多数据集模式
            Boolean multiDataSet = (Boolean) dataSourceConfig.get("multiDataSet");
            if (Boolean.TRUE.equals(multiDataSet)) {
                return getMultiExternalDataTable(dataSourceConfig);
            }

            // 单数据集模式（原有逻辑）
            String dataSetId = (String) dataSourceConfig.get("dataSetId");
            if (dataSetId == null || dataSetId.isEmpty()) {
                result.put("success", false);
                result.put("error", "未指定数据集ID");
                return result;
            }

            // 获取表格字段配置
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> tableFields = (List<Map<String, Object>>) dataSourceConfig.get("tableFields");

            if (tableFields == null || tableFields.isEmpty()) {
                result.put("success", false);
                result.put("error", "未配置表格字段");
                return result;
            }

            // 调用数据集服务获取数据
            DataSourceConnector.QueryResult queryResult = dataSetService.executeDataSet(dataSetId);

            if (!queryResult.isSuccess()) {
                result.put("success", false);
                result.put("error", "获取数据集数据失败: " + queryResult.getMessage());
                return result;
            }

            List<Map<String, Object>> rawData = queryResult.getData();

            if (rawData == null || rawData.isEmpty()) {
                result.put("success", true);
                result.put("data", new ArrayList<>());
                return result;
            }

            // 根据字段配置转换数据
            List<Map<String, Object>> tableData = new ArrayList<>();

            for (Map<String, Object> rawRow : rawData) {
                Map<String, Object> tableRow = new HashMap<>();

                // 根据配置的字段映射转换数据
                for (Map<String, Object> fieldConfig : tableFields) {
                    String displayName = (String) fieldConfig.get("displayName");
                    String dataField = (String) fieldConfig.get("dataField");

                    if (displayName != null && dataField != null) {
                        Object value = rawRow.get(dataField);
                        tableRow.put(displayName, value != null ? value : "");
                    }
                }

                tableData.add(tableRow);
            }

            result.put("success", true);
            result.put("data", tableData);

        } catch (Exception e) {
            log.error("获取外部数据源表格数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取多外部数据源表格数据
     */
    private Map<String, Object> getMultiExternalDataTable(Map<String, Object> dataSourceConfig) {
        Map<String, Object> result = new HashMap<>();

        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> dataSets = (List<Map<String, Object>>) dataSourceConfig.get("dataSets");

            if (dataSets == null || dataSets.isEmpty()) {
                result.put("success", false);
                result.put("error", "未配置数据集");
                return result;
            }

            String mergeStrategy = (String) dataSourceConfig.getOrDefault("mergeStrategy", "union");
            log.info("处理多外部数据源表格数据，数据集数量: {}, 合并策略: {}", dataSets.size(), mergeStrategy);

            List<Map<String, Object>> allTableData = new ArrayList<>();
            Set<String> allColumns = new LinkedHashSet<>();
            List<String> failedDataSets = new ArrayList<>();

            // 处理每个数据集
            for (int i = 0; i < dataSets.size(); i++) {
                Map<String, Object> dataSetConfig = dataSets.get(i);
                String dataSetId = (String) dataSetConfig.get("dataSetId");
                String alias = (String) dataSetConfig.getOrDefault("alias", "数据集" + (i + 1));

                try {
                    if (dataSetId == null || dataSetId.isEmpty()) {
                        failedDataSets.add(alias + ": 未指定数据集ID");
                        continue;
                    }

                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> tableFields = (List<Map<String, Object>>) dataSetConfig.get("tableFields");

                    if (tableFields == null || tableFields.isEmpty()) {
                        failedDataSets.add(alias + ": 未配置表格字段");
                        continue;
                    }

                    // 调用数据集服务获取数据
                    DataSourceConnector.QueryResult queryResult = dataSetService.executeDataSet(dataSetId);

                    if (!queryResult.isSuccess()) {
                        failedDataSets.add(alias + ": " + queryResult.getMessage());
                        continue;
                    }

                    List<Map<String, Object>> rawData = queryResult.getData();
                    if (rawData == null || rawData.isEmpty()) {
                        log.warn("数据集 {} 返回空数据", alias);
                        continue;
                    }

                    // 根据字段配置转换数据
                    List<Map<String, Object>> dataSetTableData = new ArrayList<>();
                    for (Map<String, Object> rawRow : rawData) {
                        Map<String, Object> tableRow = new HashMap<>();

                        // 根据配置的字段映射转换数据
                        for (Map<String, Object> fieldConfig : tableFields) {
                            String displayName = (String) fieldConfig.get("displayName");
                            String dataField = (String) fieldConfig.get("dataField");

                            if (displayName != null && dataField != null) {
                                Object value = rawRow.get(dataField);
                                tableRow.put(displayName, value != null ? value : "");
                                allColumns.add(displayName);
                            }
                        }

                        // 根据合并策略添加数据源标识
                        if ("union".equals(mergeStrategy)) {
                            tableRow.put("数据源", alias);
                            allColumns.add("数据源");
                        }

                        dataSetTableData.add(tableRow);
                    }

                    // 根据合并策略处理数据
                    if ("separate".equals(mergeStrategy) && !allTableData.isEmpty()) {
                        // 分离模式：添加分隔行
                        Map<String, Object> separatorRow = new HashMap<>();
                        for (String column : allColumns) {
                            separatorRow.put(column, "---");
                        }
                        allTableData.add(separatorRow);

                        // 添加数据集标题行
                        Map<String, Object> titleRow = new HashMap<>();
                        boolean first = true;
                        for (String column : allColumns) {
                            titleRow.put(column, first ? alias : "");
                            first = false;
                        }
                        allTableData.add(titleRow);
                    }

                    allTableData.addAll(dataSetTableData);

                } catch (Exception e) {
                    log.error("处理数据集 {} 失败", alias, e);
                    failedDataSets.add(alias + ": " + e.getMessage());
                }
            }

            // 构建结果
            result.put("success", true);
            result.put("data", allTableData);
            result.put("columns", new ArrayList<>(allColumns));

            if (!failedDataSets.isEmpty()) {
                result.put("warnings", failedDataSets);
                log.warn("部分数据集处理失败: {}", failedDataSets);
            }

            log.info("多外部数据源表格数据处理完成，总行数: {}, 总列数: {}",
                    allTableData.size(), allColumns.size());

        } catch (Exception e) {
            log.error("获取多外部数据源表格数据失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }
}
