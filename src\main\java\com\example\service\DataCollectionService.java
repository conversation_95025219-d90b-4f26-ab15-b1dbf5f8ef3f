package com.example.service;

import com.example.model.DataHistory;
import com.example.model.DataItem;
import com.example.repository.DataHistoryRepository;
import com.example.repository.DataItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataCollectionService {
    private final ModbusService modbusService;
    private final DataItemRepository dataItemRepository;
    private final DataHistoryRepository dataHistoryRepository;
    private final Map<String, Long> lastCollectionTime = new ConcurrentHashMap<>();
    private static final ZoneId ZONE_ID = ZoneId.of("Asia/Shanghai");
    
    @Scheduled(fixedRate = 100) // 每100ms执行一次
    @Transactional
    public void collectData() {
        List<DataItem> dataItems = dataItemRepository.findAll();
        long currentTime = System.currentTimeMillis();
        
        for (DataItem item : dataItems) {
            try {
                // 检查设备是否连接
                if (!item.getDevice().getConnected()) {
                    continue;  // 如果设备未连接，跳过此监控项
                }
                
                // 检查是否需要采集数据
                Long lastTime = lastCollectionTime.get(item.getId());
                if (lastTime == null || (currentTime - lastTime) >= item.getRefreshInterval()) {
                    // 读取数据
                    Map<String, Object> result = modbusService.readRegister(
                        item.getDevice().getId(),
                        item.getAddress()
                    );
                    
                    // 获取值
                    int value = (int) result.get("value");
                    
                    // 更新最后采集时间
                    lastCollectionTime.put(item.getId(), currentTime);
                    
                    // 更新数据项的最新值
                    item.setLatestValue(value);
                    dataItemRepository.save(item);
                    
                    // 如果历史记录功能已启用，则保存历史记录
                    if (item.getHistoryEnabled()) {
                        // 创建历史记录
                        DataHistory history = new DataHistory();
                        history.setDataItem(item);
                        history.setDataItemName(item.getName());
                        history.setDataItemAddress(item.getAddress());
                        history.setValue(value);
                        history.setDeviceId(item.getDevice().getId());
                        history.setDeviceName(item.getDevice().getName());
                        history.setTimestamp(LocalDateTime.now(ZONE_ID).truncatedTo(ChronoUnit.SECONDS));
                        
                        // 保存到数据库
                        dataHistoryRepository.save(history);
                        
                        log.debug("Saved history data for item {}: {}", item.getName(), value);
                    }
                    
                    log.debug("Collected data for item {}: {}", item.getName(), value);
                }
            } catch (Exception e) {
                log.error("Error collecting data for item {}: {}", item.getName(), e.getMessage());
            }
        }
    }
    
    @Scheduled(cron = "0 */10 * * * *") // 每10分钟执行一次
    @Transactional
    public void cleanOldData() {
        List<DataItem> dataItems = dataItemRepository.findAll();
        
        for (DataItem item : dataItems) {
            try {
                // 如果未启用历史记录，跳过
                if (!item.getHistoryEnabled()) {
                    continue;
                }
                
                // 获取保留时间
                Integer retentionHours = item.getHistoryRetentionHours();
                
                // 如果是-1，表示永久保存，跳过
                if (retentionHours != null && retentionHours == -1) {
                    continue;
                }
                
                // 默认30天（720小时）
                if (retentionHours == null) {
                    retentionHours = 720;
                }
                
                // 计算截止时间
                LocalDateTime cutoffTime = LocalDateTime.now(ZONE_ID)
                    .minusHours(retentionHours)
                    .truncatedTo(ChronoUnit.SECONDS);
                    
                // 删除该监控项的过期数据
                dataHistoryRepository.deleteByDataItemAndTimestampBefore(item, cutoffTime);
                
                log.debug("Cleaned data history for item {} before {}", item.getName(), cutoffTime);
            } catch (Exception e) {
                log.error("Error cleaning old data for item {}: {}", item.getName(), e.getMessage());
            }
        }
    }
} 